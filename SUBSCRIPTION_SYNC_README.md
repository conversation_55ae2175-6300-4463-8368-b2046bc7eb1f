# Subscription Synchronization Implementation

## Overview

This implementation adds a subscription synchronization mechanism that runs on homepage load to ensure Firebase data aligns with Paystack subscription status. This addresses the critical issue where payments succeed on Paystack but user subscription tiers in Firebase don't get updated.

## Key Components

### 1. Core Sync Function (`src/lib/paystack-service.ts`)

- **`syncUserSubscription(userId, userEmail)`**: Main synchronization function
- Fetches current subscription status from Paystack API
- Compares with Firebase user tier data
- Updates Firebase to match Paystack (Paystack is source of truth)
- Includes comprehensive error handling and logging with `[SYNC]` prefix

### 2. Sync Hook (`src/hooks/use-subscription-sync.ts`)

- **`useSubscriptionSync()`**: React hook for managing sync operations
- Implements rate limiting (10-minute cooldown between syncs)
- Includes failure backoff (exponential backoff for failed attempts)
- Provides cache management and status checking
- Integrates with existing subscription context

### 3. Homepage Integration (`src/app/page.tsx`)

- Automatically runs sync when authenticated users visit homepage
- Shows toast notification if subscription is updated
- Runs silently in background without disrupting user experience
- Respects rate limiting to avoid excessive API calls

## Features

### Rate Limiting & Caching
- **Primary Rate Limit**: 10 minutes between sync attempts per user
- **Failure Backoff**: Exponential backoff for failed sync attempts (max 1 hour)
- **Cache Management**: Prevents duplicate API calls and tracks sync status
- **Performance Optimized**: Integrates with existing 5-minute subscription context cache

### Error Handling
- Comprehensive error categorization (Firebase, Paystack, validation errors)
- Graceful handling of API failures without disrupting user experience
- Detailed logging with `[SYNC]` prefix for easy debugging
- No error toasts shown to users (background operation)

### Subscription Status Mapping
- **Active subscriptions**: `active` and `non-renewing` statuses are treated as paid tiers
- **Plan code mapping**: Maps Paystack plan codes to Firebase tier values
- **Unknown plans**: Defaults to FREE tier for unrecognized plan codes
- **Cancelled/Expired**: Properly downgrades to FREE tier

## Testing

### Manual Testing

1. **Console Testing** (Development):
   ```javascript
   // Run all tests
   window.testSubscriptionSync.runAllTests();
   
   // Test specific functionality
   window.testSubscriptionSync.testTierMapping();
   window.testSubscriptionSync.testSyncHookLogic();
   ```

2. **Real User Testing**:
   - Create test user with Paystack subscription
   - Manually modify Firebase tier to create mismatch
   - Visit homepage and check logs for sync activity
   - Verify Firebase tier gets updated to match Paystack

### Test Scenarios

1. **Tier Mismatch**: User has PRO subscription in Paystack but FREE in Firebase
2. **No Subscription**: User has no active Paystack subscription but paid tier in Firebase
3. **Rate Limiting**: Multiple homepage visits within 10 minutes
4. **API Failures**: Paystack API errors or Firebase connection issues
5. **New Users**: Users without existing Firebase usage documents

## Configuration

### Environment Variables Required
- `NEXT_PUBLIC_BASIC_PLAN_CODE`: Paystack plan code for BASIC tier
- `NEXT_PUBLIC_PRO_PLAN_CODE`: Paystack plan code for PRO tier
- `PAYSTACK_SECRET_KEY`: Paystack API secret key

### Logging
All sync operations are logged with `[SYNC]` prefix for easy filtering:
- Sync start/completion
- Tier comparisons and updates
- Rate limiting decisions
- Error details and categorization

## Integration Points

### Existing Systems
- **Paystack Service**: Uses existing `getUserSubscription()` function
- **Firebase**: Uses existing patterns for updating `userUsage` documents
- **Subscription Context**: Refreshes cached data after successful sync
- **Auth Context**: Integrates with user authentication state

### Data Flow
1. User visits homepage → Sync hook triggered
2. Check rate limiting → Skip if too recent
3. Fetch Paystack subscription → Get current status
4. Fetch Firebase tier → Get stored tier
5. Compare tiers → Determine if sync needed
6. Update Firebase → Use Paystack as source of truth
7. Refresh context → Update cached subscription data
8. Show notification → If tier was updated

## Monitoring

### Success Indicators
- `[SYNC] Successfully updated Firebase tier from X to Y`
- `[SYNC] Tiers match (X), no sync needed`
- Toast notification shown to user when tier updated

### Warning Indicators
- `[SYNC] Skipping sync - rate limited`
- `[SYNC] Warning: Unknown plan code: X`
- `[SYNC] Sync failed, failure count: X`

### Error Indicators
- `[SYNC] Error during subscription sync:`
- `[SYNC] Firebase update failed:`
- `[SYNC] Paystack API error:`

## Future Enhancements

1. **Admin Dashboard**: View sync statistics and failed attempts
2. **Webhook Backup**: Enhanced webhook reliability to reduce need for sync
3. **Batch Processing**: Sync multiple users during maintenance windows
4. **Metrics Collection**: Track sync success rates and performance
5. **User Notifications**: Optional email notifications for tier changes

## Troubleshooting

### Common Issues
1. **Rate Limited**: Normal behavior, sync will retry after cooldown
2. **API Errors**: Check Paystack API status and credentials
3. **Firebase Errors**: Verify Firebase connection and permissions
4. **Unknown Plan Codes**: Check environment variable configuration

### Debug Steps
1. Check browser console for `[SYNC]` logs
2. Verify environment variables are set correctly
3. Test Paystack API connectivity manually
4. Check Firebase security rules for `userUsage` collection
5. Verify user authentication state

This implementation provides a robust solution to the subscription synchronization problem while maintaining performance and user experience standards.
