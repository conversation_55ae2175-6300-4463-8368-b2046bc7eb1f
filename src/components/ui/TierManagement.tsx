"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth-context';
import { useUsageService, SubscriptionTier } from '@/lib/usage-service';
import { usePaystackService, PaystackSubscription } from '@/lib/paystack-service';
import { useSubscription } from '@/hooks/use-subscription';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { Badge } from './badge';
import { Loader2, CheckCircle, AlertCircle, CreditCard } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { logger } from '@/lib/logger';
import { formatPriceWithUsd } from '@/lib/currency-service';

// Component to display price with USD conversion
function PriceDisplay({ zarAmount }: { zarAmount: number }) {
  const [displayPrice, setDisplayPrice] = useState(`R${zarAmount}`);
  const [isConverting, setIsConverting] = useState(false);

  useEffect(() => {
    if (zarAmount === 0) {
      setDisplayPrice('R0');
      return;
    }

    const convertPrice = async () => {
      setIsConverting(true);
      try {
        const priceWithUsd = await formatPriceWithUsd(zarAmount);
        setDisplayPrice(priceWithUsd);
      } catch (error) {
        logger.error('[PriceDisplay] Currency conversion failed:', error);
        // Keep the ZAR price if conversion fails
        setDisplayPrice(`R${zarAmount}`);
      } finally {
        setIsConverting(false);
      }
    };

    convertPrice();
  }, [zarAmount]);

  return (
    <p className="text-3xl font-bold">
      {displayPrice}
      {isConverting && zarAmount > 0 && (
        <span className="text-sm text-muted-foreground ml-2">
          (converting...)
        </span>
      )}
    </p>
  );
}

// Helper function to determine if a subscription is renewing
const isSubscriptionRenewing = (subscription: PaystackSubscription | null): boolean => {
  if (!subscription) return false;
  // 'active' status means the subscription will auto-renew
  return subscription.status === 'active';
};

// Helper function to determine if a subscription is active (including non-renewing)
const isSubscriptionActive = (subscription: PaystackSubscription | null): boolean => {
  if (!subscription) return false;
  // Both 'active' and 'non-renewing' subscriptions are considered active until expiration
  return subscription.status === 'active' || subscription.status === 'non-renewing';
};

export function TierManagement() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const { updateTier, LIMITS } = useUsageService();
  const { initializeSubscription, getSubscription, cancelSubscription } = usePaystackService();
  const { tier: currentTier, isLoading: subscriptionLoading, refreshSubscription } = useSubscription();

  const [isLoading, setIsLoading] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [isCanceling, setIsCanceling] = useState(false);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [activeSubscription, setActiveSubscription] = useState<PaystackSubscription | null>(null);

  useEffect(() => {
    const loadSubscriptionDetails = async () => {
      if (user && currentTier !== 'FREE') {
        setIsLoading(true);
        try {
          logger.log(`[TierManagement] User has paid tier (${currentTier}), loading subscription details...`);
          const subscription = await getSubscription();
          logger.log('[TierManagement] Subscription loaded:', subscription);

          if (subscription) {
            logger.log(`[TierManagement] Active subscription found:`, {
              code: subscription.subscription_code,
              status: subscription.status,
              plan: subscription.plan?.name || 'Unknown',
              next_payment: subscription.next_payment_date
            });
          } else {
            logger.log(`[TierManagement] No active subscription found for user`);
          }

          setActiveSubscription(subscription);
        } catch (subscriptionError) {
          logger.error('[TierManagement] Error loading subscription:', subscriptionError);
          setActiveSubscription(null);
        } finally {
          setIsLoading(false);
        }
      } else {
        logger.log('[TierManagement] User has FREE tier, no subscription to load');
        setActiveSubscription(null);
      }
    };

    // Only load subscription details if we have a user and tier info is loaded
    if (user && !subscriptionLoading) {
      loadSubscriptionDetails();
    }
  }, [user, currentTier, subscriptionLoading, getSubscription]);

  const handleUpgrade = async (newTier: SubscriptionTier) => {
    if (newTier === 'FREE') {
      logger.log(`[TierManagement] Handling downgrade to FREE, activeSubscription:`, activeSubscription);
      // For free tier, we need to show confirmation dialog if there's an active subscription
      if (activeSubscription) {
        logger.log(`[TierManagement] Showing confirmation dialog for subscription cancellation`);
        setShowCancelConfirm(true);
      } else {
        logger.log(`[TierManagement] No active subscription found, updating tier directly`);
        // No active subscription, just update tier
        setIsLoading(true);
        try {
          await updateTier('FREE');
          await refreshSubscription(); // Refresh subscription context

          toast({
            title: "Plan Updated",
            description: "Your plan has been downgraded to FREE.",
          });
        } catch (error) {
          logger.error('Error downgrading plan:', error);
          toast({
            variant: "destructive",
            title: "Error",
            description: "Failed to downgrade your plan",
          });
        } finally {
          setIsLoading(false);
        }
      }
      return;
    }

    if (!user) {
      toast({
        variant: "destructive",
        title: "Authentication Required",
        description: "Please sign in to upgrade your plan.",
      });
      router.push('/auth/signin');
      return;
    }

    setIsInitializing(true);
    logger.log(`[TierManagement] Initializing upgrade to ${newTier} tier`);

    try {
      // Check if user already has an active subscription
      if (activeSubscription) {
        logger.log("[TierManagement] User has existing subscription. Attempting to cancel before upgrade...", activeSubscription);
        // Cancel existing subscription before creating a new one
        const result = await cancelSubscription(
          activeSubscription.subscription_code,
          activeSubscription.email_token
        );

        if (!result) {
          logger.error("[TierManagement] Failed to cancel existing subscription. Continuing with upgrade anyway...");
          toast({
            variant: "destructive",
            title: "Warning",
            description: "Could not cancel existing subscription. You may need to cancel manually.",
          });
          // We'll continue with the upgrade despite cancellation failure
        } else {
          logger.log("[TierManagement] Existing subscription cancelled successfully");

          // Add a longer delay to ensure Paystack's system registers the cancellation
          await new Promise(resolve => setTimeout(resolve, 3000));
        }

        // Update local state regardless of API success
        setActiveSubscription(null);
      } else {
        logger.log("[TierManagement] No existing subscription found, proceeding with new subscription");
      }

      // Store the intended tier in localStorage to verify later
      localStorage.setItem('pendingSubscriptionTier', newTier);
      logger.log(`[TierManagement] Saved pending subscription tier to localStorage: ${newTier}`);

      // Initialize payment with Paystack
      logger.log(`[TierManagement] Initializing payment with Paystack for tier: ${newTier}`);
      const response = await initializeSubscription(newTier);

      // Redirect to Paystack checkout page
      if (response && response.authorization_url) {
        logger.log(`[TierManagement] Redirecting to Paystack checkout: ${response.authorization_url}`);
        // Redirect to Paystack checkout page
        window.location.href = response.authorization_url;
      } else {
        throw new Error('Failed to initialize payment');
      }
    } catch (error) {
      logger.error('[TierManagement] Error initializing payment:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to initialize payment process",
      });
      // Clear the localStorage item if payment fails
      localStorage.removeItem('pendingSubscriptionTier');
    } finally {
      setIsInitializing(false);
    }
  };

  const handleReactivateSubscription = async () => {
    // Since Paystack doesn't support true subscription reactivation,
    // create a new subscription using the same tier as the current one
    logger.log('[TierManagement] Creating new subscription for current tier:', currentTier);

    if (!user) {
      toast({
        variant: "destructive",
        title: "Authentication Required",
        description: "Please sign in to create a new subscription.",
      });
      router.push('/auth/signin');
      return;
    }

    if (currentTier === 'FREE') {
      logger.error('[TierManagement] Cannot reactivate FREE tier subscription');
      toast({
        variant: "destructive",
        title: "Error",
        description: "Cannot create subscription for FREE tier.",
      });
      return;
    }

    toast({
      title: "Creating New Subscription",
      description: "You'll be redirected to payment. This will start a new billing cycle immediately.",
    });

    setIsInitializing(true);
    logger.log(`[TierManagement] Initializing new subscription for tier: ${currentTier}`);

    try {
      // Check if user already has an active subscription (shouldn't happen for reactivation, but safety check)
      if (activeSubscription) {
        logger.log("[TierManagement] User has existing subscription during reactivation. Attempting to cancel first...", activeSubscription);
        // Cancel existing subscription before creating a new one
        const result = await cancelSubscription(
          activeSubscription.subscription_code,
          activeSubscription.email_token
        );

        if (!result) {
          logger.error("[TierManagement] Failed to cancel existing subscription during reactivation. Continuing anyway...");
          toast({
            variant: "destructive",
            title: "Warning",
            description: "Could not cancel existing subscription. You may need to cancel manually.",
          });
          // We'll continue with the upgrade despite cancellation failure
        } else {
          logger.log("[TierManagement] Existing subscription cancelled successfully during reactivation");

          // Add a longer delay to ensure Paystack's system registers the cancellation
          await new Promise(resolve => setTimeout(resolve, 3000));
        }

        // Update local state regardless of API success
        setActiveSubscription(null);
      } else {
        logger.log("[TierManagement] No existing subscription found during reactivation, proceeding with new subscription");
      }

      // Store the intended tier in localStorage to verify later
      localStorage.setItem('pendingSubscriptionTier', currentTier);
      logger.log(`[TierManagement] Saved pending subscription tier to localStorage: ${currentTier}`);

      // Initialize payment with Paystack
      logger.log(`[TierManagement] Initializing payment with Paystack for tier: ${currentTier}`);
      const response = await initializeSubscription(currentTier);

      // Redirect to Paystack checkout page
      if (response && response.authorization_url) {
        logger.log(`[TierManagement] Redirecting to Paystack checkout: ${response.authorization_url}`);
        // Redirect to Paystack checkout page
        window.location.href = response.authorization_url;
      } else {
        throw new Error('Failed to initialize payment');
      }
    } catch (error) {
      logger.error('[TierManagement] Error initializing payment for reactivation:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to initialize payment process",
      });
      // Clear the localStorage item if payment fails
      localStorage.removeItem('pendingSubscriptionTier');
    } finally {
      setIsInitializing(false);
    }
  };

  const handleCancelSubscription = async () => {
    // Start the loading state
    setIsCanceling(true);
    logger.log(`[TierManagement] Starting subscription cancellation (cancel at period end), activeSubscription:`, activeSubscription);

    try {
      // If there's an active subscription, cancel it with Paystack
      if (activeSubscription) {
        logger.log(`[TierManagement] Attempting to cancel Paystack subscription:`, {
          code: activeSubscription.subscription_code,
          status: activeSubscription.status,
          plan: activeSubscription.plan?.name || 'Unknown',
          next_payment_date: activeSubscription.next_payment_date
        });

        const result = await cancelSubscription(
          activeSubscription.subscription_code,
          activeSubscription.email_token
        );

        // Check if we got a proper success response from Paystack
        logger.log(`[TierManagement] Paystack cancellation result:`, result);

        if (result === true) {
          logger.log("[TierManagement] Subscription cancellation confirmed by Paystack");

          // DO NOT downgrade tier immediately - keep current tier until expiration
          // The subscription should now be in 'non-renewing' status
          // Refresh subscription data to get updated status
          const updatedSubscription = await getSubscription();
          setActiveSubscription(updatedSubscription);
          await refreshSubscription();

          const expirationDate = new Date(activeSubscription.next_payment_date).toLocaleDateString();

          toast({
            title: "Subscription Cancelled",
            description: `Your subscription has been cancelled but you'll retain access to premium features until ${expirationDate}. It will not auto-renew.`,
          });
        } else {
          logger.error("[TierManagement] Paystack did not confirm subscription cancellation:", result);

          toast({
            variant: "destructive",
            title: "Cancellation Failed",
            description: "There was an issue cancelling your subscription with Paystack. Please try again or contact support.",
          });
        }
      } else {
        logger.log("[TierManagement] No active subscription to cancel in Paystack");

        toast({
          variant: "destructive",
          title: "No Active Subscription",
          description: "You don't have an active subscription to cancel.",
        });
      }
    } catch (error) {
      logger.error('[TierManagement] Error during cancellation process:', error);

      toast({
        variant: "destructive",
        title: "Error",
        description: "There was a problem cancelling your subscription. Please try again or contact support.",
      });
    } finally {
      setIsCanceling(false);
      setShowCancelConfirm(false);
    }
  };

  if (loading || isLoading || subscriptionLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Subscription Plans</CardTitle>
          <CardDescription>Sign in to manage your subscription</CardDescription>
        </CardHeader>
        <CardFooter>
          <Button onClick={() => router.push('/auth/signin')}>Sign In</Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <>
      {isSubscriptionActive(activeSubscription) && (
        <div className="mb-8 p-4 bg-primary/10 rounded-lg">
          <div className="flex justify-between items-center">
            <div>
              <div className="flex items-center gap-2">
                <h3 className="font-medium">Active Subscription</h3>
                {isSubscriptionRenewing(activeSubscription) ? (
                  <Badge variant="default" className="text-xs">Auto-renewing</Badge>
                ) : (
                  <Badge variant="secondary" className="text-xs">Expires on payment date</Badge>
                )}
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                {isSubscriptionRenewing(activeSubscription)
                  ? `Next payment: ${new Date(activeSubscription!.next_payment_date).toLocaleDateString()}`
                  : `Expires: ${new Date(activeSubscription!.next_payment_date).toLocaleDateString()}`
                }
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Plan: {activeSubscription!.plan?.name || "Unknown"} (Status: {activeSubscription!.status})
              </p>
              {!isSubscriptionRenewing(activeSubscription) && (
                <p className="text-xs text-muted-foreground mt-2 font-medium">
                  To resubscribe, you'll need to create a new subscription with immediate billing.
                </p>
              )}
            </div>
            {isSubscriptionRenewing(activeSubscription) ? (
              <Button
                variant="outline"
                size="sm"
                className="text-destructive hover:text-destructive hover:bg-destructive/10"
                onClick={() => setShowCancelConfirm(true)}
                disabled={isCanceling}
              >
                {isCanceling ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                Cancel Subscription
              </Button>
            ) : (
              <Button
                variant="outline"
                size="sm"
                className="text-primary hover:text-primary hover:bg-primary/10"
                onClick={handleReactivateSubscription}
                disabled={isInitializing || isCanceling}
              >
                {isInitializing ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                Create New Subscription
              </Button>
            )}
          </div>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-3">
        {/* Free Tier */}
        <Card className={currentTier === 'FREE' ? 'border-primary' : ''}>
          <CardHeader>
            <div className="flex justify-between items-start">
              <CardTitle>Free</CardTitle>
              {currentTier === 'FREE' && (
                <Badge variant="outline" className="bg-primary/10 text-primary">
                  Current Plan
                </Badge>
              )}
            </div>
            <CardDescription>Perfect for getting started</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <PriceDisplay zarAmount={0} />
              <p className="text-sm text-muted-foreground">Free forever</p>
            </div>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>{LIMITS.FREE.DAILY_UPLOADS} uploads per day</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>{LIMITS.FREE.MONTHLY_UPLOADS} uploads per month</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>Basic flashcard generation</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>Text-to-speech (requires own API key)</span>
              </li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button
              variant="outline"
              className="w-full"
              disabled={currentTier === 'FREE' || isInitializing || isCanceling}
              onClick={() => handleUpgrade('FREE')}
            >
              {isInitializing ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              {currentTier === 'FREE' ? 'Current Plan' : 'Downgrade'}
            </Button>
          </CardFooter>
        </Card>

        {/* Basic Tier */}
        <Card className={currentTier === 'BASIC' ? 'border-primary' : ''}>
          <CardHeader>
            <div className="flex justify-between items-start">
              <CardTitle>Basic</CardTitle>
              {currentTier === 'BASIC' && (
                <Badge variant="outline" className="bg-primary/10 text-primary">
                  Current Plan
                </Badge>
              )}
            </div>
            <CardDescription>More uploads for regular use</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <PriceDisplay zarAmount={29} />
              <p className="text-sm text-muted-foreground">per month</p>
            </div>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>{LIMITS.BASIC.DAILY_UPLOADS} uploads per day</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>{LIMITS.BASIC.MONTHLY_UPLOADS} uploads per month</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>Enhanced flashcard generation</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>Text-to-speech (requires own API key)</span>
              </li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button
              variant={currentTier === 'BASIC' ? 'outline' : 'default'}
              className="w-full"
              disabled={currentTier === 'BASIC' || isInitializing || isCanceling}
              onClick={() => handleUpgrade('BASIC')}
            >
              {isInitializing && currentTier !== 'BASIC' ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : currentTier !== 'BASIC' ? (
                <CreditCard className="mr-2 h-4 w-4" />
              ) : null}
              {currentTier === 'BASIC' ? 'Current Plan' : currentTier === 'PRO' ? 'Downgrade' : 'Upgrade'}
            </Button>
          </CardFooter>
        </Card>

        {/* Pro Tier */}
        <Card className={currentTier === 'PRO' ? 'border-primary' : ''}>
          <CardHeader>
            <div className="flex justify-between items-start">
              <CardTitle>Pro</CardTitle>
              {currentTier === 'PRO' && (
                <Badge variant="outline" className="bg-primary/10 text-primary">
                  Current Plan
                </Badge>
              )}
            </div>
            <CardDescription>Maximum uploads + included TTS</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <PriceDisplay zarAmount={75} />
              <p className="text-sm text-muted-foreground">per month</p>
            </div>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>{LIMITS.PRO.DAILY_UPLOADS} uploads per day</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>{LIMITS.PRO.MONTHLY_UPLOADS} uploads per month</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>Premium flashcard generation</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>Included text-to-speech (no API key needed)</span>
              </li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button
              variant={currentTier === 'PRO' ? 'outline' : 'default'}
              className="w-full"
              disabled={currentTier === 'PRO' || isInitializing || isCanceling}
              onClick={() => handleUpgrade('PRO')}
            >
              {isInitializing && currentTier !== 'PRO' ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : currentTier !== 'PRO' ? (
                <CreditCard className="mr-2 h-4 w-4" />
              ) : null}
              {currentTier === 'PRO' ? 'Current Plan' : 'Upgrade'}
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Cancel Confirmation Dialog */}
      <AlertDialog open={showCancelConfirm} onOpenChange={setShowCancelConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-orange-500" />
              Cancel Subscription
            </AlertDialogTitle>
            <AlertDialogDescription className="space-y-2">
              <p>
                Are you sure you want to cancel your subscription?
              </p>
              <div className="p-3 bg-accent/20 rounded-md border border-accent/30">
                <p className="text-sm text-accent-foreground">
                  <strong>Good news:</strong> You'll keep access to all premium features until{' '}
                  <strong>{activeSubscription ? new Date(activeSubscription.next_payment_date).toLocaleDateString() : 'your subscription expires'}</strong>.
                  Your subscription simply won't auto-renew.
                </p>
              </div>
              <div className="p-3 bg-muted rounded-md border border-border">
                <p className="text-sm text-muted-foreground">
                  <strong>Important:</strong> If you want to resubscribe,
                  you'll need to create a new subscription which will start a new billing cycle immediately.
                </p>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowCancelConfirm(false)} disabled={isCanceling}>
              Keep Subscription
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCancelSubscription}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isCanceling}
            >
              {isCanceling ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : null}
              Cancel Subscription
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}